export const GetPexelsPhoto = async (placeName, searchType = "place") => {
  const PEXELS_API_KEY = import.meta.env.VITE_PEXELS_API_KEY;

  
  let searchSuffix = "";
  if (searchType === "hotel") {
    searchSuffix = " hotel building exterior";
  } else if (searchType === "city") {
    searchSuffix = " city skyline buildings architecture";
  } else {
    searchSuffix = "";
  }

  try {
    const response = await fetch(
      `https://api.pexels.com/v1/search?query=${encodeURIComponent(placeName + searchSuffix)}&per_page=5`,
      {
        headers: {
          Authorization: PEXELS_API_KEY,
        },
      }
    );

    const data = await response.json();

    if (data.photos && data.photos.length > 0) {
      // First pass: Look for high-quality, relevant images
      for (const photo of data.photos) {
        const url = photo.src.medium;
        const alt = photo.alt?.toLowerCase() || "";

        // Block people and inappropriate content
        const isBlocked = ["portrait", "baby", "person", "woman", "man", "child", "face", "people"].some(k =>
          url.includes(k) || alt.includes(k)
        );

        // For cities, prefer images with buildings/architecture
        if (searchType === "city") {
          const hasGoodCityKeywords = ["building", "architecture", "skyline", "city", "urban", "downtown", "street", "tower"].some(k =>
            alt.includes(k)
          );

          // Block abstract/artistic images for cities
          const isAbstract = ["abstract", "art", "artistic", "sky", "cloud", "sunset", "sunrise", "nature", "landscape"].some(k =>
            alt.includes(k) && !alt.includes("city") && !alt.includes("building")
          );

          if (!isBlocked && !isAbstract && hasGoodCityKeywords) {
            return url;
          }
        } else if (searchType === "hotel") {
          const hasHotelKeywords = ["hotel", "building", "entrance", "exterior", "architecture"].some(k =>
            alt.includes(k)
          );

          if (!isBlocked && hasHotelKeywords) {
            return url;
          }
        } else {
          // For places, use basic filtering
          if (!isBlocked) {
            return url;
          }
        }
      }

      // Second pass: If no ideal image found, use any non-blocked image
      for (const photo of data.photos) {
        const url = photo.src.medium;
        const alt = photo.alt?.toLowerCase() || "";

        const isBlocked = ["portrait", "baby", "person", "woman", "man", "child", "face", "people"].some(k =>
          url.includes(k) || alt.includes(k)
        );

        if (!isBlocked) {
          return url;
        }
      }
    }

    return null; 
  } catch (error) {
    console.error("Pexels API error:", error);
    return null;
  }
};
